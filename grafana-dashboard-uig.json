{"dashboard": {"id": null, "title": "UI Gateway (UIG) Monitoring Dashboard", "tags": ["uig", "ui-gateway", "prometheus", "spring-boot"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Overview", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "Application Uptime", "type": "stat", "targets": [{"expr": "process_uptime_seconds{application=\"$application\"}", "legendFormat": "Uptime (seconds)"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 300}, {"color": "green", "value": 3600}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}}, {"id": 3, "title": "Active WebSocket Sessions", "type": "stat", "targets": [{"expr": "uig_websocket_sessions_count{server_name=\"$server\"}", "legendFormat": "Active Sessions"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}}, {"id": 4, "title": "Current Login Count", "type": "stat", "targets": [{"expr": "uig_login_count{server_name=\"$server\"}", "legendFormat": "Logged In Users"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 20}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}}, {"id": 5, "title": "Total Subscriptions", "type": "stat", "targets": [{"expr": "uig_custom_subscriptions_count{server_name=\"$server\"} + uig_std_subscriptions_count{server_name=\"$server\"}", "legendFormat": "Total Subscriptions"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "value"}, "mappings": []}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}}, {"id": 6, "title": "WebSocket Metrics", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}}, {"id": 7, "title": "WebSocket Session Activity", "type": "graph", "targets": [{"expr": "rate(uig_websocket_session_total{action=\"open\",server_name=\"$server\"}[5m])", "legendFormat": "Sessions Opened/sec - {{org}}"}, {"expr": "rate(uig_websocket_session_total{action=\"close\",server_name=\"$server\"}[5m])", "legendFormat": "Sessions Closed/sec - {{org}}"}], "yAxes": [{"label": "Sessions/sec", "min": 0}, {"show": false}], "legend": {"show": true, "values": false, "alignAsTable": true}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}}, {"id": 8, "title": "WebSocket Heartbeat Latency", "type": "graph", "targets": [{"expr": "uig_websocket_session_heartbeat_summary{server_name=\"$server\",quantile=\"0.5\"}", "legendFormat": "P50 Latency - {{org}}/{{user}}"}, {"expr": "uig_websocket_session_heartbeat_summary{server_name=\"$server\",quantile=\"0.95\"}", "legendFormat": "P95 Latency - {{org}}/{{user}}"}, {"expr": "uig_websocket_session_heartbeat_summary{server_name=\"$server\",quantile=\"0.99\"}", "legendFormat": "P99 Latency - {{org}}/{{user}}"}], "yAxes": [{"label": "Latency (ms)", "min": 0}, {"show": false}], "legend": {"show": true, "values": false, "alignAsTable": true}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}}, {"id": 9, "title": "Message Throughput", "type": "graph", "targets": [{"expr": "rate(uig_websocket_messages_esp_rates_total{server_name=\"$server\"}[5m])", "legendFormat": "ESP Rates/sec - {{org}}/{{currency_pair}}"}, {"expr": "rate(uig_websocket_session_heartbeat_sent_total{server_name=\"$server\"}[5m])", "legendFormat": "Heartbeats Sent/sec - {{org}}/{{user}}"}], "yAxes": [{"label": "Messages/sec", "min": 0}, {"show": false}], "legend": {"show": true, "values": false, "alignAsTable": true}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 18}}, {"id": 10, "title": "User Activity", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}}, {"id": 11, "title": "Login Activity", "type": "graph", "targets": [{"expr": "rate(uig_login_total{action=\"login\",server_name=\"$server\"}[5m])", "legendFormat": "Logins/sec - {{org}}"}, {"expr": "rate(uig_login_total{action=\"logout\",server_name=\"$server\"}[5m])", "legendFormat": "Logouts/sec - {{org}}"}], "yAxes": [{"label": "Events/sec", "min": 0}, {"show": false}], "legend": {"show": true, "values": false, "alignAsTable": true}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}}, {"id": 12, "title": "Subscription Distribution", "type": "piechart", "targets": [{"expr": "uig_custom_subscriptions_count{server_name=\"$server\"}", "legendFormat": "Custom Subscriptions"}, {"expr": "uig_std_subscriptions_count{server_name=\"$server\"}", "legendFormat": "Standard Subscriptions"}], "options": {"pieType": "pie", "tooltip": {"mode": "single"}, "legend": {"displayMode": "table", "placement": "right"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 27}}]}}