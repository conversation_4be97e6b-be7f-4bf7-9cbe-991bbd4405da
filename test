MAIN,Idc.Adaptor.MSCCXBB2L.AccountId,XCLBCCXB,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.AccountId.Required.ESP.Subscription,TRUE,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.AccountId.Required.ESP.Trade,TRUE,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Auto.Subscription.Currency.Pairs,"BTC/USD,ETH/USD,BTC/USDT,ETH/USDT",GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CADA/USD,ADA/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CATM/USD,ATM/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CAVX/USD,AVAX/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CBAT/USD,BAT/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CBCH/USD,BCH/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CBNB/USD,BNB/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CBTC/USD,BTC/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CDOT/USD,DOT/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CETH/USD,ETH/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CLTC/USD,LTC/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CSOL/USD,SOL/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CTRX/USD,TRX/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CUNI/USD,UNI/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CXMR/USD,XMR/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Integral.CurrencyPair.*.CXTZ/USD,XTZ/USD,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Multicast.Serializer.Version,17,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.OrderType.Supported,LMT,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Policy.Class.Name,com.integral.adaptor.FXGridAdaptor.nu.FXGridAdaptorPolicy,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.PQ.Supported.Time.In.Force,"IOC,FOK",GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Price.Type,MQ,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Default.ConnectionType,initiator,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Application.Class.Name,com.integral.adaptor.comm.fix.quickfix.provider.FIXProviderApplicationHandlerC,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Default.EndDay,Sunday,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Default.EndTime,17:00:00,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Default.Logon.Password,P4FFD8HtMNkhm$,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Default.Logon.User.Name,SGUser2,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Default.StartDay,Sunday,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Default.StartTime,17:00:01,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Default.TargetCompID,ldn.fxgrid,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Order.SenderCompID,order.XCLBCCXB.sg1,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Order.SocketConnectHost,*************,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Order.SocketConnectPort,6200,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Price.SenderCompID,quote.XCLBCCXB.sg1,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Price.SocketConnectHost,*************,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.QuickFIXJ.Provider.Price.SocketConnectPort,6200,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.realLPName,MSCCXB,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Send.Stream.To.Provider,TRUE,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Stream.DeliverToCompId.Mapping.LDNBRG2,CCXB,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.SubAccountId.Required.ESP.Trade,TRUE,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Supported.Streams,LDNBRG2,GROUP,GLOBALVS,UAGroup
MAIN,Idc.Adaptor.MSCCXBB2L.Venues.AutoSubcriptionEnabled,TRUE,GROUP,GLOBALVS,UAGroup
MAIN,IDC.IS.MSCCXBB2L.External.System.For.RFS.Subscription,,GLOBAL,GLOBALVS,GLOBAL
MAIN,IDC.IS.MSCCXBB2L.External.System.For.RFS.Trade,,GLOBAL,GLOBALVS,GLOBAL
MAIN,IDC.IS.MSCCXBB2L.External.System.For.Subscription,,GLOBAL,GLOBALVS,GLOBAL
MAIN,IDC.IS.MSCCXBB2L.External.System.For.Trade,,GLOBAL,GLOBALVS,GLOBAL
NUM,Namespace,ShortName,Value,Scope,VirtualServer,ServerGroup